import httpx
import logging
import os
from typing import Dict, Any

logger = logging.getLogger(__name__)

class TokenValidationService:
    def __init__(self, validation_url: str = None):
        if validation_url is None:
            validation_url = os.getenv("TOKEN_VALIDATION_URL")
        
        if not validation_url.endswith("/api/check-token/"):
            validation_url = validation_url.rstrip("/")
            if not validation_url.endswith("/api/check-token"):
                validation_url += "/api/check-token/"
            else:
                validation_url += "/"
        
        self.validation_url = validation_url
    
    async def validate_token(self, token: str) -> bool:
        logger.info(f"Validating token using URL: {self.validation_url}")
        logger.info(f"Token (first 10 chars): {token[:10]}...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.validation_url,
                    params={"token": token},
                    timeout=10.0
                )
                
                logger.info(f"Token validation response status: {response.status_code}")
                logger.info(f"Token validation response body: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    is_valid = data.get("valid", False)
                    logger.info(f"Token validation result: {is_valid}")
                    return is_valid
                else:
                    logger.warning(f"Token validation failed with status {response.status_code}: {response.text}")
                    return False
                    
        except httpx.RequestError as e:
            logger.error(f"Token validation request failed: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during token validation: {e}")
            return False

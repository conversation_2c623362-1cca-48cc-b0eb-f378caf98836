import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
import logging

load_dotenv()
logger = logging.getLogger(__name__)

@dataclass
class PotionsConfig:
    """Potions WMS Configuration settings"""

    # Authentication
    client_id: str
    client_secret: str

    # Potions API Settings
    potions_base_url: str
    
    # Integration Control
    integration_enabled: bool = True
    timeout: int = 60

    @classmethod
    def from_environment(cls) -> 'PotionsConfig':
        """Create configuration from environment variables"""

        # Integration control
        integration_enabled = os.getenv("POTIONS_INTEGRATION_ENABLED", "true").lower() == "true"

        # If integration is disabled, return minimal config
        if not integration_enabled:
            return cls(
                integration_enabled=False,
                client_id="",
                client_secret="",
                potions_base_url=""
            )

        # Required settings when integration is enabled
        client_id = os.getenv("POTIONS_CLIENT_ID")
        client_secret = os.getenv("POTIONS_CLIENT_SECRET")
        potions_base_url = os.getenv("POTIONS_BASE_URL")

        if not client_id or not client_secret or not potions_base_url:
            raise ValueError("POTIONS_CLIENT_ID, POTIONS_CLIENT_SECRET, and POTIONS_BASE_URL are required when POTIONS_INTEGRATION_ENABLED=true")

        return cls(
            # Integration Control
            integration_enabled=integration_enabled,
            # Authentication
            client_id=client_id,
            client_secret=client_secret,
            # API Settings
            potions_base_url=potions_base_url,
            timeout=int(os.getenv("POTIONS_TIMEOUT", "60")),
        )

    def validate(self) -> bool:
        """Validate the configuration"""
        if not self.integration_enabled:
            logger.info("Potions integration is disabled")
            return True

        required_fields = [
            ("client_id", self.client_id),
            ("client_secret", self.client_secret),
            ("potions_base_url", self.potions_base_url)
        ]

        missing_fields = [field for field, value in required_fields if not value]

        if missing_fields:
            logger.error(f"Missing required Potions configuration fields: {missing_fields}")
            return False

        logger.info("Potions configuration validated successfully")
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        return {
            "potions_base_url": self.potions_base_url,
            "timeout": self.timeout,
            "integration_enabled": self.integration_enabled,
            "has_credentials": bool(self.client_id and self.client_secret)
        }

class PotionsConfigManager:
    """Manages Potions configuration with validation"""
    
    def __init__(self):
        self._config: Optional[PotionsConfig] = None
    
    def get_config(self) -> Optional[PotionsConfig]:
        """Get Potions configuration with caching"""
        if self._config is None:
            try:
                self._config = PotionsConfig.from_environment()
                if not self._config.validate():
                    logger.error("Potions configuration validation failed")
                    return None
            except Exception as e:
                logger.error(f"Failed to load Potions configuration: {str(e)}")
                return None
        
        return self._config
        
    def reload_config(self) -> Optional[PotionsConfig]:
        """Force reload configuration from environment"""
        self._config = None
        return self.get_config()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for monitoring"""
        if self._config is None:
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "config": self._config.to_dict()
        }

# Global configuration manager
potions_config_manager = PotionsConfigManager()

# Convenience function to get current config
def get_potions_config() -> Optional[PotionsConfig]:
    """Get current Potions configuration"""
    return potions_config_manager.get_config()

import json
import httpx
import logging
from typing import Dict, Any
from app.integrations.potions_config import get_potions_config
from app.core.constants import OrderStatus

logger = logging.getLogger(__name__)


class PotionsService:
    """Service for integrating with Potions WMS API"""
    
    def __init__(self):
        self.config = get_potions_config()
        # Use Potions API configuration
        if self.config and self.config.integration_enabled:
            self.potions_base_url = self.config.potions_base_url
            self.client_id = self.config.client_id
            self.client_secret = self.config.client_secret
            self.timeout = float(self.config.timeout)
        else:
            # Default values when integration is disabled
            self.potions_base_url = "http://localhost:8004"
            self.client_id = ""
            self.client_secret = ""
            self.timeout = 60.0
    
    async def sync_order_by_id(self, facility_name: str, order_id: str, order_service) -> Dict[str, Any]:
        """Sync order to Potions WMS API by order ID"""
        try:
            # Call Potions API to trigger WMS sync
            result = await self._trigger_potions_wms_sync(order_id)
            
            if result['success']:
                # Update order status to WMS_SYNCED (21)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNCED)
                logger.info(f"Order {order_id} synced to Potions WMS successfully")
                return {
                    "success": True,
                    "status": OrderStatus.WMS_SYNCED,
                    "task_id": result.get('task_id'),
                    "message": "Order synced to Potions WMS successfully"
                }
            else:
                # Update order status to WMS_SYNC_FAILED (22)
                await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
                logger.error(f"Order {order_id} failed to sync to Potions WMS: {result.get('message')}")
                return {
                    "success": False,
                    "status": OrderStatus.WMS_SYNC_FAILED,
                    "error": result.get('error'),
                    "message": f"Failed to sync order to Potions WMS: {result.get('message')}"
                }
                
        except Exception as e:
            # Update order status to WMS_SYNC_FAILED (22) on exception
            await order_service.update_order_status(order_id, OrderStatus.WMS_SYNC_FAILED)
            logger.error(f"Exception while syncing order {order_id} to Potions WMS: {str(e)}")
            return {
                "success": False,
                "status": OrderStatus.WMS_SYNC_FAILED,
                "error": str(e),
                "message": "Exception occurred while syncing to Potions WMS"
            }
    
    async def _trigger_potions_wms_sync(self, order_id: str) -> Dict[str, Any]:
        """Trigger Potions WMS sync via API call"""
        try:
            # Get OAuth token
            token = await self._get_oauth_token()
            if not token:
                return {
                    "success": False,
                    "error": "Authentication failed",
                    "message": "Failed to get OAuth token from Potions"
                }
            
            # Call Potions WMS API
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}'
            }
            
            payload = {
                "order_id": order_id
            }
            
            endpoint = f"{self.potions_base_url}/api/potions/integrations/order/create/"
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"Calling Potions WMS API: {endpoint}")
                logger.info(f"Payload: {json.dumps(payload, indent=2)}")
                
                response = await client.post(
                    endpoint,
                    headers=headers,
                    json=payload
                )
                
                if response.status_code in [200, 201, 202]:
                    response_data = response.json() if response.content else {}
                    logger.info(f"Potions WMS API success: {response_data}")
                    return {
                        "success": True,
                        "task_id": response_data.get('task_id'),
                        "message": "Order sync triggered successfully in Potions"
                    }
                else:
                    error_msg = response.text
                    logger.error(f"Potions WMS API error: {response.status_code} - {error_msg}")
                    return {
                        "success": False,
                        "error": f"Potions API error: {response.status_code}",
                        "message": error_msg
                    }
                    
        except Exception as e:
            logger.error(f"Exception while calling Potions WMS API: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Exception occurred while calling Potions WMS API"
            }
    
    async def _get_oauth_token(self) -> str:
        """Get OAuth token from Potions API"""
        try:
            token_endpoint = f"{self.potions_base_url}/o/token/"
            
            token_data = {
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret
            }
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                logger.info(f"Getting OAuth token from: {token_endpoint}")
                
                response = await client.post(
                    token_endpoint,
                    headers=headers,
                    data=token_data
                )
                
                if response.status_code == 200:
                    token_response = response.json()
                    access_token = token_response.get('access_token')
                    logger.info("OAuth token obtained successfully")
                    return access_token
                else:
                    logger.error(f"OAuth token request failed: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Exception while getting OAuth token: {str(e)}")
            return None
    
    async def cancel_outbound_order(self, order_reference: str, warehouse: str = None) -> Dict[str, Any]:
        """Cancel order in Potions WMS (placeholder for future implementation)"""
        logger.info(f"Order cancellation not implemented in Potions WMS for order: {order_reference}")
        return {
            "success": True,
            "message": "Order cancellation not implemented in Potions WMS - handled in OMS only"
        }
    
    async def process_return(self, return_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process return in Potions WMS (placeholder for future implementation)"""
        logger.info(f"Return processing not implemented in Potions WMS")
        return {
            "success": True,
            "message": "Return processing not implemented in Potions WMS - handled in OMS only"
        }


# Create a singleton instance
potions_service = PotionsService()

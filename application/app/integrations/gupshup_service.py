import os
import logging
import httpx
from typing import Dict, Any, Optional
from fastapi import HTTPException

logger = logging.getLogger(__name__)


class GupshupService:
    """Service for sending WhatsApp messages via Gupshup API."""
    
    def __init__(self):
        self.userid = os.getenv("GUPSHUP_USERID")
        self.password = os.getenv("GUPSHUP_PASSWORD")
        self.base_url = "https://media.smsgupshup.com/GatewayAPI/rest"
        
        if not self.userid or not self.password:
            logger.warning("Gupshup configuration missing. WhatsApp notifications will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info("Gupshup WhatsApp service initialized")
    
    async def send_whatsapp_message(
        self, 
        phone_number: str, 
        message: str, 
        template_name: Optional[str] = None,
        template_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send WhatsApp message via Gupshup API.
        
        Args:
            phone_number: Recipient's phone number (with country code)
            message: Message text (for simple messages)
            template_name: Template name (for template messages)
            template_params: Template parameters
            
        Returns:
            API response from Gupshup
        """
        if not self.enabled:
            logger.warning("Gupshup service is disabled. Skipping WhatsApp message.")
            return {"status": "disabled", "message": "Gupshup service not configured"}
        
        try:
            # Format phone number (remove + if present and ensure it starts with country code)
            formatted_phone = phone_number.replace("+", "").replace(" ", "")
            
            # Prepare payload for Gupshup API
            payload = {
                "userid": self.userid,
                "password": self.password,
                "send_to": formatted_phone,
                "msg": message,
                "msg_type": "TEXT",
                "method": "SendMessage",
                "format": "json",
                "v": "1.1",
                "auth_scheme": "plain",
                "channel": "WHATSAPP"  # Explicitly specify WhatsApp channel
            }
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.base_url,
                    data=payload,
                    headers=headers,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"WhatsApp message sent successfully to {formatted_phone}")
                    logger.info(f"Gupshup API response: {result}")
                    return result
                else:
                    logger.error(f"Failed to send WhatsApp message: {response.status_code} - {response.text}")
                    return {
                        "status": "error",
                        "error": f"HTTP {response.status_code}",
                        "message": response.text
                    }
                    
        except Exception as e:
            logger.error(f"Error sending WhatsApp message: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def send_sms_message(
        self, 
        phone_number: str, 
        message: str
    ) -> Dict[str, Any]:
        """
        Send SMS message via Gupshup API.
        
        Args:
            phone_number: Recipient's phone number (with country code)
            message: Message text
            
        Returns:
            API response from Gupshup
        """
        if not self.enabled:
            logger.warning("Gupshup service is disabled. Skipping SMS message.")
            return {"status": "disabled", "message": "Gupshup service not configured"}
        
        try:
            # Format phone number (remove + if present and ensure it starts with country code)
            formatted_phone = phone_number.replace("+", "").replace(" ", "")
            
            # Prepare payload for Gupshup SMS API
            payload = {
                "userid": self.userid,
                "password": self.password,
                "send_to": formatted_phone,
                "msg": message,
                "msg_type": "TEXT",
                "method": "SendMessage",
                "format": "json",
                "v": "1.1",
                "auth_scheme": "plain"
            }
            
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.base_url,
                    data=payload,
                    headers=headers,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"SMS message sent successfully to {formatted_phone}")
                    logger.info(f"Gupshup SMS API response: {result}")
                    return result
                else:
                    logger.error(f"Failed to send SMS message: {response.status_code} - {response.text}")
                    return {
                        "status": "error",
                        "error": f"HTTP {response.status_code}",
                        "message": response.text
                    }
                    
        except Exception as e:
            logger.error(f"Error sending SMS message: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def send_order_confirmation(
        self, 
        phone_number: str, 
        order_id: str, 
        customer_name: str, 
        total_amount: float,
        estimated_delivery: str,
        items_summary: str
    ) -> Dict[str, Any]:
        """
        Send order confirmation WhatsApp message.
        
        Args:
            phone_number: Customer's phone number
            order_id: Order ID
            customer_name: Customer name
            total_amount: Order total amount
            estimated_delivery: Estimated delivery time
            items_summary: Summary of ordered items
            
        Returns:
            API response from Gupshup
        """
        message = f"नमस्ते {customer_name}, आपका ऑर्डर {order_id} जिसकी कीमत ₹{total_amount:.2f} है, 24 घंटों के अंदर डिलीवर कर दिया जाएगा। -Team Rozana"

        return await self.send_whatsapp_message(phone_number, message)
    
    async def send_order_status_update(
        self, 
        phone_number: str, 
        order_id: str, 
        status: str, 
        customer_name: str
    ) -> Dict[str, Any]:
        """
        Send order status update WhatsApp message.
        
        Args:
            phone_number: Customer's phone number
            order_id: Order ID
            status: New order status
            customer_name: Customer name
            
        Returns:
            API response from Gupshup
        """
        status_hindi = {
            "confirmed": "कन्फर्म",
            "processing": "प्रोसेसिंग",
            "packed": "पैक",
            "shipped": "शिप",
            "out_for_delivery": "डिलीवरी के लिए निकला",
            "delivered": "डिलीवर्ड",
            "cancelled": "कैंसल"
        }
        
        status_text = status_hindi.get(status.lower(), status.title())
        
        message = f"नमस्ते {customer_name}, आपके ऑर्डर {order_id} का स्टेटस अपडेट हो गया है। नया स्टेटस: {status_text} -Team Rozana"

        return await self.send_whatsapp_message(phone_number, message)
    
    async def send_payment_confirmation(
        self, 
        phone_number: str, 
        order_id: str, 
        customer_name: str, 
        amount: float,
        payment_method: str
    ) -> Dict[str, Any]:
        """
        Send payment confirmation WhatsApp message.
        
        Args:
            phone_number: Customer's phone number
            order_id: Order ID
            customer_name: Customer name
            amount: Payment amount
            payment_method: Payment method used
            
        Returns:
            API response from Gupshup
        """
        message = f"नमस्ते {customer_name}, आपका पेमेंट ₹{amount:.2f} ऑर्डर {order_id} के लिए सफलतापूर्वक प्रोसेस हो गया है। आपका ऑर्डर अब प्रोसेस हो रहा है। -Team Rozana"

        return await self.send_whatsapp_message(phone_number, message)


# Global instance
gupshup_service = GupshupService() 
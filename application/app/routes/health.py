from fastapi import APIRouter, BackgroundTasks
from app.integrations.gupshup_service import gupshup_service
from app.core.order_functions import send_order_confirmation_whatsapp, get_items_summary
from app.dto.orders import OrderCreate, OrderItemCreate, PaymentInfo

router = APIRouter(tags=["health"])


@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "4.0.0",
        "service": "rozana-oms"
    }


@router.post("/test-whatsapp")
async def test_whatsapp(background_tasks: BackgroundTasks):
    """Test WhatsApp integration endpoint."""
    try:
        # Test message
        test_phone = "**********"  # Test phone number provided by user
        test_message = "नमस्ते! यह Rozana OMS API से एक टेस्ट मैसेज है। अगर आपको यह मैसेज मिल रहा है, तो WhatsApp integration काम कर रही है! 🎉"
        
        # Send test message in background
        background_tasks.add_task(
            gupshup_service.send_whatsapp_message,
            test_phone,
            test_message
        )
        
        return {
            "success": True,
            "message": "Test WhatsApp message scheduled",
            "phone": test_phone,
            "note": "Check the phone number for the test message"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Failed to schedule test message: {str(e)}"
        }


@router.post("/test-order-whatsapp")
async def test_order_whatsapp(background_tasks: BackgroundTasks):
    """Test order creation with WhatsApp notification."""
    try:
        # Test order details
        test_phone = "**********"
        customer_name = "Test Customer"
        order_id = "TEST-ORDER-001"
        total_amount = 299.99
        eta = "2024-01-15T18:00:00"
        
        # Sample order items
        items = [
            {"sku": "PROD-001", "quantity": 2, "unit_price": 100.00, "sale_price": 99.99},
            {"sku": "PROD-002", "quantity": 1, "unit_price": 150.00, "sale_price": 100.00}
        ]
        
        items_summary = get_items_summary([OrderItemCreate(**item) for item in items])
        
        # Send order confirmation WhatsApp message
        background_tasks.add_task(
            send_order_confirmation_whatsapp,
            test_phone,
            order_id,
            customer_name,
            total_amount,
            eta,
            items_summary
        )
        
        return {
            "success": True,
            "message": "Test order WhatsApp notification scheduled",
            "order_id": order_id,
            "customer_name": customer_name,
            "phone": test_phone,
            "total_amount": total_amount,
            "items_summary": items_summary,
            "note": "Check WhatsApp for order confirmation message"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Failed to schedule order notification: {str(e)}"
        }


@router.post("/test-sms")
async def test_sms(background_tasks: BackgroundTasks):
    """Test SMS sending via Gupshup."""
    try:
        test_phone = "**********"
        test_message = "🧪 Test SMS from Rozana OMS API. If you receive this, SMS is working! 🎉"
        
        # Send SMS message
        background_tasks.add_task(
            gupshup_service.send_sms_message,
            test_phone,
            test_message
        )
        
        return {
            "success": True,
            "message": "Test SMS scheduled",
            "phone": test_phone,
            "note": "Check SMS for test message"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Failed to schedule SMS: {str(e)}"
        }
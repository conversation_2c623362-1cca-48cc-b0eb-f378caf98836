from fastapi import APIRouter, Request, Query, BackgroundTasks
from app.dto.orders import OrderCreate, OrderResponse, OrderCancelRequest, OrderCancelResponse, OrderPartialReturnRequest, OrderFullReturnRequest, OrderReturnResponse  # type: ignore
from app.dto.order_again import OrderAgainResponse
from app.dto.encryption import EncryptCustomerCodeRequest, EncryptCustomerCodeResponse
from app.services.order_query_service import OrderQueryService
from app.dto.orders import OrderStatusUpdate, OrderItemStatusUpdate  # type: ignore
from app.core.order_functions import (
    create_order_core,
    get_order_details_core,
    get_all_orders_core,
)

from app.core.order_updates import (
    update_order_status_core,
    update_item_status_core,
)
from app.core.order_cancel import cancel_order_core
from app.core.order_return import return_order_items_core, return_full_order_core
from app.core.encryption_core import encrypt_customer_code_core

app_router = APIRouter(tags=["app"])


@app_router.post("/create_order", response_model=OrderResponse)
async def create_order(order: OrderCreate, request: Request, background_tasks: BackgroundTasks):
    """Create order via mobile app."""
    return await create_order_core(order, request, background_tasks, "app")


@app_router.get("/order_details")
async def get_order_details(order_id: str = Query(..., description="Order ID")):
    """Retrieve single order details via mobile app."""
    return await get_order_details_core(order_id)


@app_router.get("/orders")
async def get_all_orders(request: Request, limit: int = 20, offset: int = 0, sort_order: str = "desc"):
    """List orders for logged-in mobile user."""
    return await get_all_orders_core(request, limit, offset, sort_order)


@app_router.get("/order_again", response_model=OrderAgainResponse)
async def order_again(request: Request, limit: int = 20, offset: int = 0):
    user_id = getattr(request.state, "user_id", None)
    if not user_id:
        return OrderAgainResponse(products=[])

    service = OrderQueryService()
    products = service.get_order_again_products(user_id=user_id, limit=limit, offset=offset)
    return OrderAgainResponse(products=products)

# ---- Update endpoints ----
@app_router.put("/update_order_status")
@app_router.patch("/update_order_status")
async def update_order_status(order_update: OrderStatusUpdate, background_tasks: BackgroundTasks):
    """Update order status (mobile)."""
    return await update_order_status_core(order_update, background_tasks)


@app_router.put("/update_item_status")
@app_router.patch("/update_item_status")
async def update_item_status(item_update: OrderItemStatusUpdate):
    """Update individual item status (mobile)."""
    return await update_item_status_core(item_update)


@app_router.post("/cancel_order", response_model=OrderCancelResponse)
async def cancel_order(cancel_request: OrderCancelRequest):
    return await cancel_order_core(cancel_request.order_id)


# ---- Return endpoints ----
@app_router.post("/return_items", response_model=OrderReturnResponse)
async def return_order_items(return_request: OrderPartialReturnRequest):
    """Return specific items from an order."""
    items_to_return = [{"sku": item.sku, "quantity": item.quantity} for item in return_request.items]
    return await return_order_items_core(return_request.order_id, items_to_return)


@app_router.post("/return_full_order", response_model=OrderReturnResponse)
async def return_full_order(return_request: OrderFullReturnRequest):
    """Return all items in an order."""
    return await return_full_order_core(return_request.order_id)

@app_router.post("/encrypt_customer_code", response_model=EncryptCustomerCodeResponse)
async def encrypt_customer_code(request_data: EncryptCustomerCodeRequest, request: Request):
    """Encrypt customer code using AES encryption with Firebase token validation."""
    return await encrypt_customer_code_core(request_data.customer_code, request)

"""
API routes for token validation and order operations
"""
from fastapi import <PERSON>Rout<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, HTTPException
from typing import Optional
from pydantic import BaseModel

from app.core.token_validation_core import (
    validate_token_and_get_orders,
    validate_token_and_get_order_items,
    validate_token_and_cancel_order,
    validate_token_and_return_items,
    validate_token_and_return_full_order
)

api_router = APIRouter(tags=["api"])

# Request models (no token needed in body as it's in header)
class CancelOrderRequest(BaseModel):
    customer_id: str
    order_id: str

class ReturnItemRequest(BaseModel):
    sku: str
    quantity: int

class PartialReturnRequest(BaseModel):
    customer_id: str
    order_id: str
    items: list[ReturnItemRequest]

class FullReturnRequest(BaseModel):
    customer_id: str
    order_id: str

@api_router.get("/get_orders")
async def get_orders(
    customer_id: str = Query(..., description="Customer ID"),
    limit: int = Query(20, description="Number of orders to return"),
    offset: int = Query(0, description="Offset for pagination"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    authorization: str = Header(..., description="Bearer token for authentication")
):
    # Extract token from Authorization header (Bearer token)
    token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
    
    return await validate_token_and_get_orders(
        token=token,
        customer_id=customer_id,
        limit=limit,
        offset=offset,
        sort_order=sort_order
    )

@api_router.get("/order_items")
async def get_order_items(
    customer_id: str = Query(..., description="Customer ID"),
    order_id: str = Query(..., description="Order ID to get items for"),
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Get order items for a specific order after token validation
    """
    # Extract token from Authorization header (Bearer token)
    token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
    
    return await validate_token_and_get_order_items(
        token=token,
        customer_id=customer_id,
        order_id=order_id
    )

@api_router.post("/cancel_order")
async def cancel_order(
    cancel_request: CancelOrderRequest,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Cancel an order for a customer after token validation
    """
    # Extract token from Authorization header (Bearer token)
    token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
    
    return await validate_token_and_cancel_order(
        token=token,
        customer_id=cancel_request.customer_id,
        order_id=cancel_request.order_id
    )

@api_router.post("/return_items")
async def return_items(
    return_request: PartialReturnRequest,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Return specific items from an order after token validation
    """
    # Extract token from Authorization header (Bearer token)
    token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
    
    # Convert items to the format expected by the core function
    items_to_return = [{"sku": item.sku, "quantity": item.quantity} for item in return_request.items]
    
    return await validate_token_and_return_items(
        token=token,
        customer_id=return_request.customer_id,
        order_id=return_request.order_id,
        items_to_return=items_to_return
    )

@api_router.post("/return_full_order")
async def return_full_order(
    return_request: FullReturnRequest,
    authorization: str = Header(..., description="Bearer token for authentication")
):
    """
    Return all items in an order after token validation
    """
    # Extract token from Authorization header (Bearer token)
    token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
    
    return await validate_token_and_return_full_order(
        token=token,
        customer_id=return_request.customer_id,
        order_id=return_request.order_id
    )

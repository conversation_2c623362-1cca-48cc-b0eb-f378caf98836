DATABASE_URL=
ALEMBIC_DATABASE_URL=
ALLOWED_ORIGINS= #add comma separated list of allowed origins
STOCK_CHECK_ENABLED=true
REDIS_URL=

# WMS Integration
WMS_INTEGRATION_ENABLED=true
WMS_CLIENT_ID=your_wms_client_id
WMS_CLIENT_SECRET=your_wms_client_secret
WMS_BASE_URL=base_url

RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
RAZORPAY_BASE_URL=https://api.razorpay.com/v1
RAZORPAY_CURRENCY=INR
RAZORPAY_TIMEOUT=30
RAZORPAY_INTEGRATION_ENABLED=true

# Sentry Configuration
SENTRY_ENABLED=false
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development
SENTRY_RELEASE=rozana-oms-service@1.0.0
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1


OTEL_SDK_DISABLED=true
OTEL_SERVICE_NAME=rozana-oms-service
OTEL_SERVICE_VERSION=4.0.0
OTEL_EXPORTER_OTLP_ENDPOINT=
OTEL_EXPORTER_OTLP_HEADERS=signoz-access-token=your_ingestion_key_here
OTEL_EXPORTER_OTLP_PROTOCOL=grpc
OTEL_EXPORTER_OTLP_INSECURE=false
OTEL_INSTRUMENTATION_HTTP_EXCLUDED_URLS=/health
OTEL_LOGS_EXPORTER=otlp
OTEL_METRICS_EXPORTER=otlp
OTEL_TRACES_EXPORTER=otlp
ENVIRONMENT=development

#Auth Integration
TOKEN_VALIDATION_URL=http://host.docker.internal:8000/api/check-token/

# Typesense Integration
TYPESENSE_HOST=localhost
TYPESENSE_PORT=8108
TYPESENSE_PROTOCOL=http
TYPESENSE_API_KEY=your_typesense_api_key
TYPESENSE_COLLECTION_NAME=facility_products

# Price Validation
PRICE_CHECK_ENABLED=true

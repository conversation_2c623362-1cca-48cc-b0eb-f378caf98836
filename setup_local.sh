#!/bin/bash

echo "Setting up Rozana OMS Service for local development..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file..."
    cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/oms_db

# Application Configuration
LOG_LEVEL=INFO
ENVIRONMENT=development

# Firebase Configuration (if needed)
# FIREBASE_PROJECT_ID=your-project-id
# FIREBASE_PRIVATE_KEY_ID=your-private-key-id
# FIREBASE_PRIVATE_KEY=your-private-key
# FIREBASE_CLIENT_EMAIL=your-client-email
# FIREBASE_CLIENT_ID=your-client-id
# FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
# FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
# FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
# FIREBASE_CLIENT_X509_CERT_URL=your-cert-url

# Gupshup WhatsApp Configuration
GUPSHUP_USERID=**********
GUPSHUP_PASSWORD=*WchbXCp
EOF
    echo ".env file created successfully!"
else
    echo ".env file already exists."
fi

echo "Setup complete! You can now:"
echo "1. Install Docker Desktop (recommended for full setup)"
echo "2. Or run the application locally with: cd application && source venv/bin/activate && uvicorn app.main:app --reload"
echo "3. Make sure PostgreSQL is running on localhost:5432" 